import jsonlines
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import re
import string
import nltk
from nltk.corpus import stopwords
from nltk.stem import WordNetLemmatizer
from nltk.tokenize import word_tokenize
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score

def read_jsonl_to_dataframe(file_path):
    """
    Read a JSONL file and convert it to a pandas DataFrame
    """
    data = []
    with open(file_path, 'r', encoding='utf-8') as file:
        for line in file:
            data.append(json.loads(line.strip()))
    return pd.DataFrame(data)

# Load the math_500.jsonl file
df = read_jsonl_to_dataframe('math_500_response_generated.jsonl')

# Display basic information about the DataFrame
print(f"DataFrame shape: {df.shape}")
print(f"\nColumn names: {list(df.columns)}")
print(f"\nData types:")
print(df.dtypes)
print(f"\nFirst few rows:")
print(df.head())

# Show some sample content from the query_and_response column
print(f"\nSample query content:")
for i in range(3):
    if i < len(df):
        query_content = df.iloc[i]['query_and_response'][0]['content']
        print(f"Row {i}: {query_content[:100]}...")

df.head()

# make a new column named 'dataset' and fill it with the 'id's filtered value before the '_'
df['dataset'] = df['id'].apply(lambda x: x.split('_')[0])
df.head()

df['dataset'].value_counts()

#extract unique values from the 'dataset' column\
unique_datasets = df['dataset'].unique()

for dataset in unique_datasets:
    print(f"Dataset: {dataset}")
    temp_df = df[df['dataset']==dataset]
    for i in range(len(temp_df)):
        if i > 5:
            break    
        print(f"Example {i}: {temp_df.iloc[i]['query_and_response'][0]['content']}\n")
        print(f"Example {i}: {temp_df.iloc[i]['query_and_response'][1]['content']}")
    print("================================================================\n================================================================")

webinstruct_df = df[df['dataset']=='WebInstructSub-prometheus']

for i in range(len(webinstruct_df)):
    if i > 5:
        break    
    print(f"Example {i}: {webinstruct_df.iloc[i]['query_and_response'][0]['content']}\n")
    print(f"Example {i}: {webinstruct_df.iloc[i]['query_and_response'][1]['content']}\n")
#    print(f"Example {i}: {webinstruct_df.iloc[i]['query_and_response'][0]['content']}")

numina_df = df[df['dataset']=='NuminaMath-CoT']

for i in range(len(numina_df)):
    #if i > 20:
    #    break
    print(f"Example {i}: {numina_df.iloc[i]['query_and_response'][0]['content']}")

OpenMathInstruct_df = df[df['dataset']=='OpenMathInstruct-2']

for i in range(len(OpenMathInstruct_df)):
    if i > 20:
       break
    print(f"Example {i}: {OpenMathInstruct_df.iloc[i]['query_and_response'][0]['content']}")

wildchat_df = df[df['dataset']=='WildChat-1M']

for i in range(len(wildchat_df)):
    print(f"Example {i}: {wildchat_df.iloc[i]['query_and_response'][0]['content']}")


